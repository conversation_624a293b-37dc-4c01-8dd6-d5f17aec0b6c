import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Dialog } from '@/components/ui/Dialog'
import { FileUpload } from '@/components/FileUpload'
import { Project, UploadedFile } from '@/types'
import { saveProject, generateId, addLog } from '@/lib/storage'
import { X, Upload } from 'lucide-react'

interface CreateProjectDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProjectCreated?: () => void
}

export function CreateProjectDialog({ open, onOpenChange, onProjectCreated }: CreateProjectDialogProps) {
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    committee: '',
    delegate: '',
    agenda: '',
    context: ''
  })
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.committee || !formData.delegate || !formData.agenda) {
      addLog({
        level: 'warning',
        message: 'Missing required fields for project creation'
      })
      return
    }

    setIsLoading(true)
    
    try {
      const project: Project = {
        id: generateId(),
        committee: formData.committee,
        delegate: formData.delegate,
        agenda: formData.agenda,
        context: formData.context,
        files,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'draft',
        interactions: [],
        links: [],
        logs: []
      }

      saveProject(project)
      
      addLog({
        level: 'success',
        message: 'Created new project',
        details: {
          projectId: project.id,
          committee: project.committee,
          delegate: project.delegate
        }
      })

      onOpenChange(false)
      onProjectCreated?.() // Notify parent to refresh project list
      navigate(`/project/${project.id}`)
    } catch (error) {
      addLog({
        level: 'error',
        message: 'Failed to create project',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    setFormData({
      committee: '',
      delegate: '',
      agenda: '',
      context: ''
    })
    setFiles([])
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
        <div className="glass-dialog max-w-3xl w-full max-h-[90vh] overflow-y-auto shadow-2xl border border-white/20">
          {/* Header */}
          <div className="flex items-center justify-between p-8 border-b border-white/10">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Create New Project
            </h2>
            <Button
              variant="glass"
              size="icon"
              onClick={() => onOpenChange(false)}
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-8 space-y-8">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Committee *
                </label>
                <Input
                  placeholder="e.g., Security Council, ECOSOC"
                  value={formData.committee}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    committee: e.target.value
                  }))}
                  required
                  className="glass-input"
                />
              </div>

              <div className="space-y-3">
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Delegate (Country) *
                </label>
                <Input
                  placeholder="e.g., United States, Germany"
                  value={formData.delegate}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    delegate: e.target.value
                  }))}
                  required
                  className="glass-input"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Agenda *
              </label>
              <Input
                placeholder="e.g., Climate Change and International Security"
                value={formData.agenda}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  agenda: e.target.value 
                }))}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Context / Objectives
              </label>
              <textarea
                className="w-full h-24 px-3 py-2 border border-input rounded-md bg-background text-sm resize-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                placeholder="Describe your research objectives, specific questions, or context..."
                value={formData.context}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  context: e.target.value 
                }))}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Upload Files (Optional)
              </label>
              <FileUpload
                files={files}
                onFilesChange={setFiles}
                maxFiles={5}
                acceptedTypes={['pdf', 'image', 'audio']}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Upload PDFs, images, or audio files. They will be processed and converted to text.
              </p>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={isLoading}
              >
                Reset
              </Button>
              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4" />
                    Create Project
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </Dialog>
  )
}
