import { useState, useRef } from 'react'
import { Button } from '@/components/ui/Button'
import { UploadedFile } from '@/types'
import { generateId, addLog } from '@/lib/storage'
import { formatFileSize } from '@/lib/utils'
import { Upload, X, FileText, Image, Music } from 'lucide-react'

interface FileUploadProps {
  files: UploadedFile[]
  onFilesChange: (files: UploadedFile[]) => void
  maxFiles?: number
  acceptedTypes?: ('pdf' | 'image' | 'audio')[]
}

export function FileUpload({ 
  files, 
  onFilesChange, 
  maxFiles = 5, 
  acceptedTypes = ['pdf', 'image', 'audio'] 
}: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const getFileType = (file: File): 'pdf' | 'image' | 'audio' | null => {
    if (file.type.startsWith('image/')) return 'image'
    if (file.type.startsWith('audio/')) return 'audio'
    if (file.type === 'application/pdf') return 'pdf'
    return null
  }

  const getFileIcon = (type: 'pdf' | 'image' | 'audio') => {
    switch (type) {
      case 'pdf':
        return <FileText className="w-4 h-4" />
      case 'image':
        return <Image className="w-4 h-4" />
      case 'audio':
        return <Music className="w-4 h-4" />
    }
  }

  const processFile = async (file: File): Promise<UploadedFile | null> => {
    const fileType = getFileType(file)
    if (!fileType || !acceptedTypes.includes(fileType)) {
      addLog({
        level: 'warning',
        message: 'Unsupported file type',
        details: { fileName: file.name, fileType: file.type }
      })
      return null
    }

    try {
      const uploadedFile: UploadedFile = {
        id: generateId(),
        name: file.name,
        type: fileType,
        size: file.size,
        uploadedAt: new Date().toISOString()
      }

      // Process file content based on type
      try {
        const { processUploadedFile } = await import('@/lib/fileProcessing')
        const content = await processUploadedFile(file)
        uploadedFile.content = content

        addLog({
          level: 'success',
          message: 'File processed successfully',
          details: {
            fileName: file.name,
            fileType,
            fileSize: file.size,
            contentLength: content.length
          }
        })
      } catch (processingError) {
        addLog({
          level: 'warning',
          message: 'File uploaded but processing failed',
          details: {
            fileName: file.name,
            error: processingError instanceof Error ? processingError.message : 'Unknown error'
          }
        })
        // Still return the file even if processing fails
      }

      return uploadedFile
    } catch (error) {
      addLog({
        level: 'error',
        message: 'Failed to process file',
        details: {
          fileName: file.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })
      return null
    }
  }

  const handleFiles = async (fileList: FileList) => {
    if (files.length >= maxFiles) {
      addLog({
        level: 'warning',
        message: `Maximum ${maxFiles} files allowed`
      })
      return
    }

    setIsProcessing(true)
    const newFiles: UploadedFile[] = []

    for (let i = 0; i < fileList.length && files.length + newFiles.length < maxFiles; i++) {
      const processedFile = await processFile(fileList[i])
      if (processedFile) {
        newFiles.push(processedFile)
      }
    }

    onFilesChange([...files, ...newFiles])
    setIsProcessing(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    handleFiles(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const removeFile = (fileId: string) => {
    onFilesChange(files.filter(f => f.id !== fileId))
  }

  const acceptString = acceptedTypes
    .map(type => {
      switch (type) {
        case 'pdf': return '.pdf'
        case 'image': return 'image/*'
        case 'audio': return 'audio/*'
        default: return ''
      }
    })
    .join(',')

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragging 
            ? 'border-primary bg-primary/5' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
        <p className="text-sm text-gray-600 mb-2">
          Drag and drop files here, or{' '}
          <button
            type="button"
            className="text-primary hover:underline"
            onClick={() => fileInputRef.current?.click()}
          >
            browse
          </button>
        </p>
        <p className="text-xs text-gray-500">
          Supports PDF, images, and audio files (max {maxFiles} files)
        </p>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptString}
          onChange={(e) => e.target.files && handleFiles(e.target.files)}
          className="hidden"
        />
      </div>

      {/* Processing Indicator */}
      {isProcessing && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-primary rounded-full animate-spin" />
          Processing files...
        </div>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Uploaded Files</h4>
          {files.map((file) => (
            <div
              key={file.id}
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
            >
              <div className="flex items-center gap-3">
                {getFileIcon(file.type)}
                <div>
                  <p className="text-sm font-medium">{file.name}</p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(file.size)} • {file.type.toUpperCase()}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFile(file.id)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
