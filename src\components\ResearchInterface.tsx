import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Project, Interaction, ResearchPlan } from '@/types'
import { saveProject, addLog, getSettings } from '@/lib/storage'
import { generateResearchPlan, generateAIResponse } from '@/lib/openai'
import { ResearchAutomation } from '@/lib/researchAutomation'
import { PostResearchActions } from '@/components/PostResearchActions'
import { formatDate } from '@/lib/utils'
import {
  Send,
  ThumbsUp,
  ThumbsDown,
  Edit3,
  FileText,
  Link as LinkIcon,
  MessageSquare,
  Bot,
  User
} from 'lucide-react'

interface ResearchInterfaceProps {
  project: Project
  isActive: boolean
  onProjectUpdate: (project: Project) => void
}

export function ResearchInterface({ project, isActive, onProjectUpdate }: ResearchInterfaceProps) {
  const [activeTab, setActiveTab] = useState<'context' | 'links'>('context')
  const [message, setMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [pendingPlan, setPendingPlan] = useState<ResearchPlan | null>(null)
  const [editFeedback, setEditFeedback] = useState('')
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [isAutoResearching, setIsAutoResearching] = useState(false)
  const automationRef = useRef<ResearchAutomation | null>(null)

  useEffect(() => {
    if (isActive && !project.researchPlan && !pendingPlan) {
      generateInitialPlan()
    }
  }, [isActive, project.researchPlan, pendingPlan])

  const generateInitialPlan = async () => {
    const settings = getSettings()
    if (!settings.openaiApiKey) {
      addLog({
        level: 'error',
        message: 'OpenAI API key not configured'
      })
      return
    }

    setIsLoading(true)
    try {
      const plan = await generateResearchPlan(project, settings.openaiApiKey, settings.selectedModel)
      setPendingPlan(plan)
      
      addLog({
        level: 'success',
        message: 'Generated initial research plan',
        details: { projectId: project.id }
      })
    } catch (error) {
      addLog({
        level: 'error',
        message: 'Failed to generate research plan',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      })
    } finally {
      setIsLoading(false)
    }
  }

  const approvePlan = async () => {
    if (!pendingPlan) return

    const approvedPlan = { ...pendingPlan, approved: true }
    const updatedProject = {
      ...project,
      researchPlan: approvedPlan,
      interactions: [
        ...project.interactions,
        {
          id: crypto.randomUUID(),
          type: 'system' as const,
          content: `Research plan approved. Starting automated research with ${approvedPlan.queries.length} queries.`,
          timestamp: new Date().toISOString()
        }
      ]
    }

    saveProject(updatedProject)
    onProjectUpdate(updatedProject)
    setPendingPlan(null)

    addLog({
      level: 'info',
      message: 'Research plan approved',
      details: { projectId: project.id }
    })

    // Start automated research
    if (isActive) {
      startAutomatedResearch(updatedProject)
    }
  }

  const requestPlanEdit = () => {
    setShowEditDialog(true)
  }

  const submitEditFeedback = async () => {
    if (!editFeedback.trim()) return

    const settings = getSettings()
    if (!settings.openaiApiKey) {
      addLog({
        level: 'error',
        message: 'OpenAI API key not configured'
      })
      return
    }

    setIsLoading(true)
    setShowEditDialog(false)

    try {
      // Add user feedback to interactions
      const feedbackInteraction: Interaction = {
        id: crypto.randomUUID(),
        type: 'user',
        content: `Edit request: ${editFeedback}`,
        timestamp: new Date().toISOString()
      }

      // Generate revised plan based on feedback
      const revisedPlan = await generateResearchPlan(
        { ...project, context: `${project.context}\n\nUser feedback: ${editFeedback}` },
        settings.openaiApiKey,
        settings.selectedModel
      )

      setPendingPlan(revisedPlan)
      setEditFeedback('')

      const updatedProject = {
        ...project,
        interactions: [...project.interactions, feedbackInteraction]
      }

      saveProject(updatedProject)
      onProjectUpdate(updatedProject)

      addLog({
        level: 'info',
        message: 'Generated revised research plan',
        details: { projectId: project.id }
      })
    } catch (error) {
      addLog({
        level: 'error',
        message: 'Failed to generate revised plan',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      })
    } finally {
      setIsLoading(false)
    }
  }

  const sendMessage = async () => {
    if (!message.trim()) return

    const settings = getSettings()
    if (!settings.openaiApiKey) {
      addLog({
        level: 'error',
        message: 'OpenAI API key not configured'
      })
      return
    }

    const userMessage: Interaction = {
      id: crypto.randomUUID(),
      type: 'user',
      content: message,
      timestamp: new Date().toISOString()
    }

    const updatedInteractions = [...project.interactions, userMessage]
    setMessage('')
    setIsLoading(true)

    try {
      // Prepare conversation context
      const messages = updatedInteractions
        .filter(i => i.type !== 'system')
        .map(i => ({
          role: i.type === 'user' ? 'user' : 'assistant',
          content: i.content
        }))

      const response = await generateAIResponse(
        messages,
        settings.openaiApiKey,
        settings.selectedModel,
        `You are helping with MUN research for ${project.delegate} in ${project.committee} on the topic: ${project.agenda}`
      )

      const aiMessage: Interaction = {
        id: crypto.randomUUID(),
        type: 'ai',
        content: response.content,
        timestamp: new Date().toISOString(),
        metadata: response.metadata
      }

      const finalInteractions = [...updatedInteractions, aiMessage]
      const updatedProject = {
        ...project,
        interactions: finalInteractions
      }

      saveProject(updatedProject)
      onProjectUpdate(updatedProject)

      addLog({
        level: 'info',
        message: 'AI response generated',
        details: { projectId: project.id }
      })
    } catch (error) {
      addLog({
        level: 'error',
        message: 'Failed to generate AI response',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      })
    } finally {
      setIsLoading(false)
    }
  }

  const startAutomatedResearch = async (projectToUse: Project = project) => {
    if (isAutoResearching) return

    setIsAutoResearching(true)

    try {
      const { ResearchAutomation } = await import('@/lib/researchAutomation')
      automationRef.current = new ResearchAutomation(projectToUse, onProjectUpdate)
      await automationRef.current.startAutomatedResearch()
    } catch (error) {
      addLog({
        level: 'error',
        message: 'Failed to start automated research',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      })
    } finally {
      setIsAutoResearching(false)
      automationRef.current = null
    }
  }

  const stopAutomatedResearch = () => {
    if (automationRef.current) {
      automationRef.current.stop()
      automationRef.current = null
    }
    setIsAutoResearching(false)
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (automationRef.current) {
        automationRef.current.stop()
      }
    }
  }, [])

  return (
    <div className="flex h-[calc(100vh-80px)]">
      {/* Left Pane - Interactions */}
      <div className="w-1/2 border-r flex flex-col">
        <div className="p-4 border-b bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <h2 className="font-semibold flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Research Assistant
            </h2>
            {isAutoResearching && (
              <div className="flex items-center gap-2 text-sm text-blue-600">
                <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                Auto-researching...
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={stopAutomatedResearch}
                  className="text-red-600 hover:text-red-700"
                >
                  Stop
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {/* Pending Plan Approval */}
          {pendingPlan && !project.researchPlan && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <h3 className="font-medium mb-2">Research Plan Generated</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <strong>Approach:</strong>
                  <p className="mt-1">{pendingPlan.approach}</p>
                </div>
                <div>
                  <strong>Objectives:</strong>
                  <ul className="mt-1 list-disc list-inside space-y-1">
                    {pendingPlan.objectives.map((obj, idx) => (
                      <li key={idx}>{obj}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <strong>Search Queries:</strong>
                  <ul className="mt-1 list-disc list-inside space-y-1">
                    {pendingPlan.queries.map((query, idx) => (
                      <li key={idx}>{query}</li>
                    ))}
                  </ul>
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <Button onClick={approvePlan} size="sm" disabled={isAutoResearching}>
                  <ThumbsUp className="w-4 h-4 mr-1" />
                  {isAutoResearching ? 'Starting...' : 'Approve & Start Research'}
                </Button>
                <Button variant="outline" onClick={requestPlanEdit} size="sm" disabled={isAutoResearching}>
                  <Edit3 className="w-4 h-4 mr-1" />
                  Request Edits
                </Button>
              </div>
            </div>
          )}

          {/* Conversation */}
          {project.interactions.map((interaction) => (
            <div
              key={interaction.id}
              className={`flex gap-3 ${
                interaction.type === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`max-w-[80%] rounded-lg p-3 ${
                  interaction.type === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : interaction.type === 'ai'
                    ? 'bg-gray-100 dark:bg-gray-700'
                    : 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200'
                }`}
              >
                <div className="flex items-center gap-2 mb-1">
                  {interaction.type === 'user' ? (
                    <User className="w-4 h-4" />
                  ) : interaction.type === 'ai' ? (
                    <Bot className="w-4 h-4" />
                  ) : (
                    <FileText className="w-4 h-4" />
                  )}
                  <span className="text-xs opacity-75">
                    {formatDate(interaction.timestamp)}
                  </span>
                </div>
                <div className="text-sm whitespace-pre-wrap">
                  {interaction.content}
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
                <span className="text-sm">AI is thinking...</span>
              </div>
            </div>
          )}
        </div>

        {/* Message Input */}
        <div className="p-4 border-t">
          <div className="flex gap-2">
            <Input
              placeholder="Ask a question or request assistance..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && sendMessage()}
              disabled={isLoading || !isActive}
            />
            <Button 
              onClick={sendMessage} 
              disabled={isLoading || !message.trim() || !isActive}
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Right Pane - Content */}
      <div className="w-1/2 flex flex-col">
        {/* Tabs */}
        <div className="border-b">
          <div className="flex">
            <button
              className={`px-4 py-3 text-sm font-medium border-b-2 ${
                activeTab === 'context'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('context')}
            >
              <FileText className="w-4 h-4 inline mr-2" />
              Context & Notes
            </button>
            <button
              className={`px-4 py-3 text-sm font-medium border-b-2 ${
                activeTab === 'links'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('links')}
            >
              <LinkIcon className="w-4 h-4 inline mr-2" />
              Links & Citations ({project.links.length})
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {activeTab === 'context' ? (
            <>
              <div>
                <h3 className="font-medium mb-3">Research Context</h3>
                <textarea
                  className="w-full h-96 p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Your research notes and context will appear here..."
                  value={project.context}
                  onChange={(e) => {
                    const updatedProject = { ...project, context: e.target.value }
                    saveProject(updatedProject)
                    onProjectUpdate(updatedProject)
                  }}
                />
              </div>

              {/* Post-Research Actions */}
              <PostResearchActions
                project={project}
                onProjectUpdate={onProjectUpdate}
              />
            </>
          ) : (
            <div>
              <h3 className="font-medium mb-3">Links & Citations</h3>
              {project.links.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No links added yet. Links will appear here as research progresses.
                </p>
              ) : (
                <div className="space-y-3">
                  {project.links.map((link) => (
                    <div key={link.id} className="border rounded-lg p-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">
                            [{link.citationNumber}] {link.title}
                          </h4>
                          <a
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs text-blue-600 hover:underline"
                          >
                            {link.url}
                          </a>
                          <p className="text-sm text-gray-600 mt-1">
                            {link.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Edit Feedback Dialog */}
      {showEditDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div className="p-4 border-b">
              <h3 className="font-semibold">Request Plan Edits</h3>
            </div>
            <div className="p-4">
              <textarea
                className="w-full h-24 p-3 border rounded-lg resize-none"
                placeholder="Describe what changes you'd like to the research plan..."
                value={editFeedback}
                onChange={(e) => setEditFeedback(e.target.value)}
              />
            </div>
            <div className="p-4 border-t flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditDialog(false)
                  setEditFeedback('')
                }}
              >
                Cancel
              </Button>
              <Button onClick={submitEditFeedback} disabled={!editFeedback.trim()}>
                Submit Feedback
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
