import { useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { HomePage } from './pages/HomePage'
import { ProjectPage } from './pages/ProjectPage'
import { SettingsPage } from './pages/SettingsPage'
import { ErrorBoundary, setupGlobalErrorHandling } from './components/ErrorBoundary'
import { addLog } from './lib/storage'

function App() {
  useEffect(() => {
    // Setup global error handling
    setupGlobalErrorHandling()

    // Log application start
    addLog({
      level: 'info',
      message: 'MUN Research Assistant started',
      details: {
        version: '1.0.0',
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }
    })

    // Development helpers
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 MUN Research Assistant - Development Mode')
      console.log('Available dev helpers: window.munDevHelpers')
    }
  }, [])

  return (
    <ErrorBoundary>
      <Router>
        <div className="min-h-screen bg-background">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/project/:id" element={<ProjectPage />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Routes>
        </div>
      </Router>
    </ErrorBoundary>
  )
}

export default App
