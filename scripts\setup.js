#!/usr/bin/env node

/**
 * MUN Research Assistant Setup Script
 * This script helps set up the development environment and provides useful commands
 */

const fs = require('fs')
const path = require('path')

console.log('🚀 MUN Research Assistant Setup')
console.log('================================\n')

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json')
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.')
  process.exit(1)
}

// Read package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))

console.log(`📦 Project: ${packageJson.name}`)
console.log(`📋 Version: ${packageJson.version || '1.0.0'}`)
console.log(`📝 Description: ${packageJson.description || 'MUN Research Assistant'}\n`)

// Check Node.js version
const nodeVersion = process.version
const requiredNodeVersion = '18.0.0'
console.log(`🟢 Node.js version: ${nodeVersion}`)

if (parseInt(nodeVersion.slice(1)) < parseInt(requiredNodeVersion)) {
  console.warn(`⚠️  Warning: Node.js ${requiredNodeVersion}+ is recommended`)
}

// Check if dependencies are installed
const nodeModulesPath = path.join(process.cwd(), 'node_modules')
if (!fs.existsSync(nodeModulesPath)) {
  console.log('\n📥 Installing dependencies...')
  console.log('Run: npm install')
} else {
  console.log('✅ Dependencies are installed')
}

// Environment setup
console.log('\n🔧 Environment Setup')
console.log('====================')

const envExamplePath = path.join(process.cwd(), '.env.example')
const envPath = path.join(process.cwd(), '.env')

if (fs.existsSync(envExamplePath) && !fs.existsSync(envPath)) {
  console.log('📄 Creating .env file from .env.example...')
  fs.copyFileSync(envExamplePath, envPath)
  console.log('✅ .env file created')
} else if (!fs.existsSync(envPath)) {
  console.log('📄 Creating basic .env file...')
  const envContent = `# MUN Research Assistant Environment Variables
VITE_APP_NAME=MUN Research Assistant
VITE_APP_VERSION=1.0.0
NODE_ENV=development
`
  fs.writeFileSync(envPath, envContent)
  console.log('✅ Basic .env file created')
}

// API Keys setup reminder
console.log('\n🔑 API Keys Setup')
console.log('=================')
console.log('To use all features, you\'ll need:')
console.log('1. OpenAI API Key (required for AI features)')
console.log('   - Get it from: https://platform.openai.com/api-keys')
console.log('   - Add it in the Settings page after starting the app')
console.log('')
console.log('2. Firecrawl API Key (optional for web scraping)')
console.log('   - Get it from: https://firecrawl.dev')
console.log('   - Add it in the Settings page after starting the app')

// Development commands
console.log('\n🛠️  Available Commands')
console.log('=====================')
console.log('npm run dev      - Start development server')
console.log('npm run build    - Build for production')
console.log('npm run preview  - Preview production build')
console.log('npm run lint     - Run ESLint')

// Quick start guide
console.log('\n🚀 Quick Start')
console.log('==============')
console.log('1. Install dependencies: npm install')
console.log('2. Start development server: npm run dev')
console.log('3. Open http://localhost:5173 in your browser')
console.log('4. Go to Settings and add your OpenAI API key')
console.log('5. Create your first research project!')

// Development tips
console.log('\n💡 Development Tips')
console.log('===================')
console.log('• Open browser console to see development helpers')
console.log('• Use window.munDevHelpers for testing utilities')
console.log('• Check the log viewer for detailed application logs')
console.log('• All data is stored locally in your browser')

console.log('\n✨ Setup complete! Happy researching! ✨\n')
