import { Project, Settings, LogEntry, UploadedFile } from '@/types'

// Test data generators for development and testing

export function createMockProject(overrides: Partial<Project> = {}): Project {
  const baseProject: Project = {
    id: 'test-project-1',
    committee: 'Security Council',
    delegate: 'United States',
    agenda: 'Climate Change and International Security',
    context: 'Initial research context for testing purposes. This project focuses on the intersection of climate change and international security, examining how environmental challenges affect global stability.',
    files: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    status: 'draft',
    interactions: [
      {
        id: 'interaction-1',
        type: 'system',
        content: 'Project created successfully',
        timestamp: new Date().toISOString()
      }
    ],
    links: [],
    logs: []
  }

  return { ...baseProject, ...overrides }
}

export function createMockProjectWithResearch(): Project {
  return createMockProject({
    status: 'completed',
    researchPlan: {
      id: 'plan-1',
      approach: 'Comprehensive analysis of climate security nexus',
      objectives: [
        'Analyze US policy on climate security',
        'Identify key international frameworks',
        'Research recent developments and initiatives'
      ],
      queries: [
        'US climate security policy 2024',
        'UN climate security resolutions',
        'climate change national security threats'
      ],
      approved: true,
      createdAt: new Date().toISOString()
    },
    links: [
      {
        id: 'link-1',
        url: 'https://example.com/climate-security-report',
        title: 'Climate Security: A National Security Imperative',
        description: 'Comprehensive analysis of climate change impacts on national security, including threat assessments and policy recommendations.',
        citationNumber: 1,
        addedAt: new Date().toISOString()
      },
      {
        id: 'link-2',
        url: 'https://example.com/un-climate-resolution',
        title: 'UN Security Council Resolution on Climate and Security',
        description: 'Latest UN Security Council resolution addressing the relationship between climate change and international peace and security.',
        citationNumber: 2,
        addedAt: new Date().toISOString()
      }
    ],
    interactions: [
      {
        id: 'interaction-1',
        type: 'system',
        content: 'Project created successfully',
        timestamp: new Date().toISOString()
      },
      {
        id: 'interaction-2',
        type: 'ai',
        content: 'Based on the research conducted, here is a comprehensive summary of the US position on climate security...',
        timestamp: new Date().toISOString(),
        metadata: { type: 'research_summary' }
      }
    ]
  })
}

export function createMockSettings(): Settings {
  return {
    openaiApiKey: '',
    selectedModel: 'gpt-4',
    firecrawlApiKey: ''
  }
}

export function createMockFile(type: 'pdf' | 'image' | 'audio' = 'pdf'): UploadedFile {
  const baseFile: UploadedFile = {
    id: `file-${Date.now()}`,
    name: `test-document.${type === 'pdf' ? 'pdf' : type === 'image' ? 'jpg' : 'mp3'}`,
    type,
    size: type === 'pdf' ? 1024000 : type === 'image' ? 512000 : 2048000,
    uploadedAt: new Date().toISOString()
  }

  if (type === 'pdf') {
    baseFile.content = '[PDF Content] This is a test PDF document containing research material relevant to the MUN topic.'
  } else if (type === 'image') {
    baseFile.content = '[Image Analysis] This image shows a chart depicting climate change impacts on global security metrics.'
  } else {
    baseFile.content = '[Audio Transcription] This is a transcribed speech about climate security policy from a recent conference.'
  }

  return baseFile
}

export function createMockLogs(): LogEntry[] {
  return [
    {
      id: 'log-1',
      level: 'info',
      message: 'Application started',
      timestamp: new Date(Date.now() - 60000).toISOString()
    },
    {
      id: 'log-2',
      level: 'success',
      message: 'Project created successfully',
      details: { projectId: 'test-project-1' },
      timestamp: new Date(Date.now() - 45000).toISOString()
    },
    {
      id: 'log-3',
      level: 'info',
      message: 'Research plan generated',
      details: { projectId: 'test-project-1', queryCount: 3 },
      timestamp: new Date(Date.now() - 30000).toISOString()
    },
    {
      id: 'log-4',
      level: 'warning',
      message: 'API rate limit approaching',
      details: { remainingRequests: 5 },
      timestamp: new Date(Date.now() - 15000).toISOString()
    }
  ]
}

// Development utilities
export function populateTestData(): void {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('Test data population is only available in development mode')
    return
  }

  try {
    // Create test projects
    const testProject1 = createMockProject()
    const testProject2 = createMockProjectWithResearch()
    
    // Store in localStorage
    localStorage.setItem('mun-research-projects', JSON.stringify([testProject1, testProject2]))
    
    // Create test logs
    const testLogs = createMockLogs()
    localStorage.setItem('mun-research-logs', JSON.stringify(testLogs))
    
    console.log('Test data populated successfully')
  } catch (error) {
    console.error('Failed to populate test data:', error)
  }
}

export function clearTestData(): void {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('Test data clearing is only available in development mode')
    return
  }

  try {
    localStorage.removeItem('mun-research-projects')
    localStorage.removeItem('mun-research-logs')
    localStorage.removeItem('mun-research-settings')
    console.log('Test data cleared successfully')
  } catch (error) {
    console.error('Failed to clear test data:', error)
  }
}

// Performance testing utilities
export function measurePerformance<T>(
  name: string,
  fn: () => T | Promise<T>
): T | Promise<T> {
  const start = performance.now()
  
  const result = fn()
  
  if (result instanceof Promise) {
    return result.then(value => {
      const end = performance.now()
      console.log(`${name} took ${end - start} milliseconds`)
      return value
    })
  } else {
    const end = performance.now()
    console.log(`${name} took ${end - start} milliseconds`)
    return result
  }
}

// Error simulation for testing
export function simulateError(probability: number = 0.1): void {
  if (Math.random() < probability) {
    throw new Error('Simulated error for testing purposes')
  }
}

// API response mocking
export function createMockAPIResponse(success: boolean = true, data?: any) {
  return {
    ok: success,
    status: success ? 200 : 500,
    statusText: success ? 'OK' : 'Internal Server Error',
    json: () => Promise.resolve(data || { success, data: success ? 'Mock data' : null })
  }
}

// Validation utilities
export function validateProject(project: Project): string[] {
  const errors: string[] = []
  
  if (!project.id) errors.push('Project ID is required')
  if (!project.committee) errors.push('Committee is required')
  if (!project.delegate) errors.push('Delegate is required')
  if (!project.agenda) errors.push('Agenda is required')
  if (!project.createdAt) errors.push('Created date is required')
  if (!project.updatedAt) errors.push('Updated date is required')
  
  return errors
}

export function validateSettings(settings: Settings): string[] {
  const errors: string[] = []
  
  if (settings.openaiApiKey && !settings.openaiApiKey.startsWith('sk-')) {
    errors.push('Invalid OpenAI API key format')
  }
  
  if (!settings.selectedModel) {
    errors.push('AI model selection is required')
  }
  
  return errors
}

// Development helpers
export const devHelpers = {
  populateTestData,
  clearTestData,
  measurePerformance,
  simulateError,
  validateProject,
  validateSettings
}

// Make available globally in development
if (process.env.NODE_ENV === 'development') {
  (window as any).munDevHelpers = devHelpers
}
