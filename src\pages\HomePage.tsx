import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/Button'
import { CreateProjectDialog } from '@/components/CreateProjectDialog'
import { LogViewer } from '@/components/LogViewer'
import { HelpSystem } from '@/components/HelpSystem'
import { Settings, Plus, FileText, HelpCircle } from 'lucide-react'

export function HomePage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const navigate = useNavigate()

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-12">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
              MUN Research Assistant
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              AI-powered research tool for Model United Nations delegates
            </p>
          </div>
          <div className="flex items-center gap-2">
            <HelpSystem />
            <Button
              variant="outline"
              onClick={() => navigate('/settings')}
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              Settings
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {/* Introduction */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">
              Welcome to Your Research Hub
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
              Streamline your MUN preparation with AI-assisted research, automated content 
              summarization, and intelligent query generation. Create comprehensive research 
              projects, gather contextual information, and prepare effective Points of Information 
              with ease.
            </p>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-start gap-3">
                <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg">
                  <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                    Intelligent Research
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    AI-powered web scraping and content analysis tailored to your committee and agenda
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="bg-green-100 dark:bg-green-900 p-2 rounded-lg">
                  <Plus className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                    Project Management
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Organize research by committee, track progress, and manage multiple projects
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={() => setIsCreateDialogOpen(true)}
              className="flex items-center gap-2 px-8 py-4 text-lg"
            >
              <Plus className="w-5 h-5" />
              Create New Project
            </Button>
          </div>
        </div>

        {/* Log Viewer */}
        <LogViewer />

        {/* Create Project Dialog */}
        <CreateProjectDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
        />
      </div>
    </div>
  )
}
