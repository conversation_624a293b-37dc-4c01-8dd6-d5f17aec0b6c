import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/Button'
import { CreateProjectDialog } from '@/components/CreateProjectDialog'
import { HelpSystem } from '@/components/HelpSystem'
import { ThemeToggle } from '@/components/ThemeToggle'
import { Project } from '@/types'
import { getProjects, deleteProject } from '@/lib/storage'
import { formatDate } from '@/lib/utils'
import { Settings, Plus, FileText, Calendar, User, Folder, ArrowRight, Trash2, RefreshCw, Search } from 'lucide-react'

export function HomePage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const navigate = useNavigate()

  useEffect(() => {
    loadProjects()
  }, [])

  const loadProjects = async () => {
    setIsLoading(true)
    try {
      // Add a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 300))
      const allProjects = getProjects()
      setProjects(allProjects)
    } finally {
      setIsLoading(false)
    }
  }

  const handleProjectCreated = () => {
    setIsCreateDialogOpen(false)
    loadProjects() // Refresh the project list
  }

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
      case 'researching':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
    }
  }

  const getStatusLabel = (status: Project['status']) => {
    switch (status) {
      case 'draft':
        return 'Draft'
      case 'researching':
        return 'Researching'
      case 'completed':
        return 'Completed'
      default:
        return 'Unknown'
    }
  }

  const handleDeleteProject = (e: React.MouseEvent, projectId: string, projectName: string) => {
    e.stopPropagation() // Prevent navigation when clicking delete

    if (window.confirm(`Are you sure you want to delete the project "${projectName}"? This action cannot be undone.`)) {
      try {
        deleteProject(projectId)
        loadProjects() // Refresh the project list
      } catch (error) {
        alert('Failed to delete project. Please try again.')
      }
    }
  }

  // Filter projects based on search term
  const filteredProjects = projects.filter(project =>
    project.committee.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.delegate.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.agenda.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-purple-900/20 dark:to-gray-800 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-400/10 via-blue-400/10 to-indigo-400/10 dark:from-purple-600/5 dark:via-blue-600/5 dark:to-indigo-600/5"></div>
      <div className="absolute top-0 left-0 w-96 h-96 bg-purple-300/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-300/20 rounded-full blur-3xl animate-pulse delay-1000"></div>

      <div className="relative container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-12">
          <div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3 animate-gradient-x">
              MUN Research Assistant
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 font-medium">
              AI-powered research tool for Model United Nations delegates
            </p>
          </div>
          <div className="flex items-center gap-3">
            <ThemeToggle />
            <HelpSystem />
            <Button
              variant="glass"
              onClick={() => navigate('/settings')}
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              Settings
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-5xl mx-auto">
          {/* Introduction */}
          <div className="glass-card p-10 mb-10 shadow-2xl border border-white/20">
            <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Welcome to Your Research Hub
            </h2>
            <p className="text-gray-700 dark:text-gray-300 mb-8 leading-relaxed text-lg">
              Streamline your MUN preparation with AI-assisted research, automated content
              summarization, and intelligent query generation. Create comprehensive research
              projects, gather contextual information, and prepare effective Points of Information
              with ease.
            </p>
            
            <div className="grid md:grid-cols-2 gap-8">
              <div className="glass-card p-6 border border-white/20 hover:scale-105 transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className="gradient-primary p-3 rounded-xl shadow-lg">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 text-lg">
                      Intelligent Research
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      AI-powered web scraping and content analysis tailored to your committee and agenda
                    </p>
                  </div>
                </div>
              </div>

              <div className="glass-card p-6 border border-white/20 hover:scale-105 transition-all duration-300">
                <div className="flex items-start gap-4">
                  <div className="gradient-accent p-3 rounded-xl shadow-lg">
                    <Plus className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 text-lg">
                      Project Management
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      Organize research by committee, track progress, and manage multiple projects
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mt-12">
            <Button
              size="lg"
              onClick={() => setIsCreateDialogOpen(true)}
              className="flex items-center gap-3 px-10 py-6 text-lg font-semibold shadow-2xl hover:shadow-purple-500/25"
            >
              <Plus className="w-6 h-6" />
              Create New Project
            </Button>
          </div>

          {/* Empty State */}
          {projects.length === 0 && (
            <div className="mt-16 text-center">
              <div className="glass-card p-12 border border-white/20 max-w-2xl mx-auto">
                <div className="gradient-primary p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
                  <Folder className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-semibold mb-4 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  No Projects Yet
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  Get started by creating your first MUN research project. Our AI will help you
                  gather comprehensive research, analyze sources, and prepare for your committee sessions.
                </p>
                <Button
                  onClick={() => setIsCreateDialogOpen(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Create Your First Project
                </Button>
              </div>
            </div>
          )}

          {/* Projects Section */}
          {projects.length > 0 && (
            <div className="mt-16">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Your Projects
                </h2>
                <div className="flex items-center gap-3">
                  {projects.length > 3 && (
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search projects..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="glass-input pl-10 pr-4 py-2 w-48 text-sm rounded-lg border border-white/20 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                      />
                    </div>
                  )}
                  <Button
                    variant="glass"
                    size="sm"
                    onClick={loadProjects}
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                  <span className="glass-card px-4 py-2 rounded-full text-sm font-medium border border-white/20">
                    {filteredProjects.length} of {projects.length} project{projects.length !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="glass-card p-6 border border-white/20 animate-pulse">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-9 h-9 bg-gray-300 dark:bg-gray-600 rounded-lg"></div>
                          <div>
                            <div className="w-32 h-5 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
                            <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                          </div>
                        </div>
                        <div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded"></div>
                      </div>
                      <div className="space-y-3 mb-4">
                        <div className="w-full h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="w-1/2 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                      <div className="pt-4 border-t border-white/10">
                        <div className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    </div>
                  ))
                ) : filteredProjects.length === 0 ? (
                  <div className="col-span-full text-center py-12">
                    <div className="glass-card p-8 border border-white/20 max-w-md mx-auto">
                      <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        No projects found
                      </h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">
                        Try adjusting your search terms or create a new project.
                      </p>
                    </div>
                  </div>
                ) : (
                  filteredProjects.map((project) => (
                  <div
                    key={project.id}
                    className="glass-card p-6 border border-white/20 hover:scale-105 hover:shadow-2xl transition-all duration-300 cursor-pointer group"
                    onClick={() => navigate(`/project/${project.id}`)}
                  >
                    {/* Project Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="gradient-primary p-2 rounded-lg shadow-lg">
                          <Folder className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white text-lg truncate">
                            {project.committee}
                          </h3>
                          <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                            {getStatusLabel(project.status)}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => handleDeleteProject(e, project.id, project.committee)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                          title="Delete project"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                        <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-purple-600 transition-colors" />
                      </div>
                    </div>

                    {/* Project Details */}
                    <div className="space-y-3 mb-4">
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                        <User className="w-4 h-4" />
                        <span className="truncate">{project.delegate}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                        <FileText className="w-4 h-4" />
                        <span className="truncate">{project.agenda}</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                        <Calendar className="w-4 h-4" />
                        <span>Updated {formatDate(project.updatedAt)}</span>
                      </div>
                    </div>

                    {/* Project Stats */}
                    <div className="flex items-center justify-between pt-4 border-t border-white/10">
                      <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                        <span>{project.links.length} sources</span>
                        <span>{project.interactions.length} interactions</span>
                        <span>{project.files.length} files</span>
                      </div>
                    </div>
                  </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>

        {/* Create Project Dialog */}
        <CreateProjectDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onProjectCreated={handleProjectCreated}
        />
      </div>
    </div>
  )
}
