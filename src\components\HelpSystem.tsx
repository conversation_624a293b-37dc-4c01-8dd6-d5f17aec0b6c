import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/Button'
import { Dialog } from '@/components/ui/Dialog'
import { 
  HelpCircle, 
  X, 
  Book, 
  Settings, 
  Upload, 
  MessageSquare,
  Download,
  ExternalLink
} from 'lucide-react'

interface HelpSystemProps {
  trigger?: React.ReactNode
}

export function HelpSystem({ trigger }: HelpSystemProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeSection, setActiveSection] = useState('getting-started')

  const helpSections = {
    'getting-started': {
      title: 'Getting Started',
      icon: Book,
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Welcome to MUN Research Assistant</h3>
          <p>This application helps Model United Nations delegates conduct comprehensive research using AI assistance.</p>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-medium">1. Configure Settings</h4>
              <p className="text-sm text-gray-600">First, go to Settings and add your OpenAI API key for AI features.</p>
            </div>
            
            <div>
              <h4 className="font-medium">2. Create a Project</h4>
              <p className="text-sm text-gray-600">Click "Create New Project" and fill in your committee, delegate country, and agenda.</p>
            </div>
            
            <div>
              <h4 className="font-medium">3. Start Research</h4>
              <p className="text-sm text-gray-600">The AI will generate a research plan. Approve it to begin automated research.</p>
            </div>
            
            <div>
              <h4 className="font-medium">4. Review Results</h4>
              <p className="text-sm text-gray-600">Interact with the AI, review gathered sources, and export your research.</p>
            </div>
          </div>
        </div>
      )
    },
    'settings': {
      title: 'Settings & Configuration',
      icon: Settings,
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">API Configuration</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">OpenAI API Key</h4>
              <p className="text-sm text-gray-600 mb-2">Required for AI features including research planning, conversations, and content generation.</p>
              <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                <li>Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">OpenAI Platform</a></li>
                <li>Keys start with "sk-" followed by a long string</li>
                <li>Your key is stored locally and never sent to our servers</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">Firecrawl API Key (Optional)</h4>
              <p className="text-sm text-gray-600 mb-2">Enables automated web scraping for research.</p>
              <ul className="text-sm text-gray-600 list-disc list-inside space-y-1">
                <li>Get your API key from <a href="https://firecrawl.dev" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Firecrawl</a></li>
                <li>Without this key, you can still use manual research features</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">AI Model Selection</h4>
              <p className="text-sm text-gray-600">Choose the AI model that best fits your needs and budget. GPT-4 provides better quality but costs more than GPT-3.5.</p>
            </div>
          </div>
        </div>
      )
    },
    'file-upload': {
      title: 'File Upload & Processing',
      icon: Upload,
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Supported File Types</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">PDF Documents</h4>
              <p className="text-sm text-gray-600">Upload research papers, reports, or documents. Text will be extracted automatically.</p>
            </div>
            
            <div>
              <h4 className="font-medium">Images</h4>
              <p className="text-sm text-gray-600">Upload charts, diagrams, or documents as images. AI will analyze and describe the content.</p>
            </div>
            
            <div>
              <h4 className="font-medium">Audio Files</h4>
              <p className="text-sm text-gray-600">Upload recordings of speeches, interviews, or meetings. They will be transcribed automatically.</p>
            </div>
          </div>
          
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Tip:</strong> File processing requires an OpenAI API key. Without it, files will be uploaded but not automatically processed.
            </p>
          </div>
        </div>
      )
    },
    'research': {
      title: 'Research Process',
      icon: MessageSquare,
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">How Research Works</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">1. Research Plan Generation</h4>
              <p className="text-sm text-gray-600">AI analyzes your project details and creates a comprehensive research plan with specific objectives and search queries.</p>
            </div>
            
            <div>
              <h4 className="font-medium">2. Plan Approval</h4>
              <p className="text-sm text-gray-600">Review the generated plan and either approve it or request modifications.</p>
            </div>
            
            <div>
              <h4 className="font-medium">3. Automated Research</h4>
              <p className="text-sm text-gray-600">The system automatically searches the web, scrapes relevant content, and summarizes findings.</p>
            </div>
            
            <div>
              <h4 className="font-medium">4. AI Interaction</h4>
              <p className="text-sm text-gray-600">Ask questions, request clarifications, or get help with specific aspects of your research.</p>
            </div>
            
            <div>
              <h4 className="font-medium">5. Content Organization</h4>
              <p className="text-sm text-gray-600">All findings are organized with citations, and you can edit the research context as needed.</p>
            </div>
          </div>
        </div>
      )
    },
    'export': {
      title: 'Export & Assistance',
      icon: Download,
      content: (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Post-Research Features</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium">Export Research Report</h4>
              <p className="text-sm text-gray-600">Download a formatted Markdown report containing all your research, sources, and findings.</p>
            </div>
            
            <div>
              <h4 className="font-medium">AI Assistance</h4>
              <p className="text-sm text-gray-600">Generate various types of content based on your research:</p>
              <ul className="text-sm text-gray-600 list-disc list-inside mt-2 space-y-1">
                <li><strong>Opening Speech:</strong> 2-3 minute opening statement for your committee</li>
                <li><strong>Points of Information:</strong> Strategic questions to ask other delegates</li>
                <li><strong>Briefing Summary:</strong> Comprehensive overview for preparation</li>
                <li><strong>Custom Content:</strong> Any specific assistance you need</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium">Copy & Share</h4>
              <p className="text-sm text-gray-600">All generated content can be copied to clipboard for use in other applications.</p>
            </div>
          </div>
        </div>
      )
    }
  }

  const defaultTrigger = (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setIsOpen(true)}
      className="flex items-center gap-2"
    >
      <HelpCircle className="w-4 h-4" />
      Help
    </Button>
  )

  return (
    <>
      {trigger ? (
        <div onClick={() => setIsOpen(true)}>
          {trigger}
        </div>
      ) : (
        defaultTrigger
      )}

      {isOpen && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-2xl font-semibold">Help & Documentation</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex h-[600px]">
                {/* Sidebar */}
                <div className="w-64 border-r bg-gray-50 dark:bg-gray-900 p-4">
                  <nav className="space-y-2">
                    {Object.entries(helpSections).map(([key, section]) => {
                      const Icon = section.icon
                      return (
                        <button
                          key={key}
                          onClick={() => setActiveSection(key)}
                          className={`w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors ${
                            activeSection === key
                              ? 'bg-primary text-primary-foreground'
                              : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                          }`}
                        >
                          <Icon className="w-4 h-4" />
                          <span className="text-sm">{section.title}</span>
                        </button>
                      )
                    })}
                  </nav>
                </div>

                {/* Content */}
                <div className="flex-1 p-6 overflow-y-auto">
                  {helpSections[activeSection as keyof typeof helpSections]?.content}
                </div>
              </div>

              {/* Footer */}
              <div className="border-t p-4 bg-gray-50 dark:bg-gray-900">
                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>MUN Research Assistant v1.0.0</span>
                  <div className="flex items-center gap-4">
                    <a
                      href="https://github.com/your-repo"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 hover:text-gray-900 dark:hover:text-gray-100"
                    >
                      <ExternalLink className="w-3 h-3" />
                      GitHub
                    </a>
                    <a
                      href="mailto:<EMAIL>"
                      className="hover:text-gray-900 dark:hover:text-gray-100"
                    >
                      Support
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Dialog>
      )}
    </>
  )
}
