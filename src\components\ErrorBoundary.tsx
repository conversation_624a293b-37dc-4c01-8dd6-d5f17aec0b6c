import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Button } from '@/components/ui/Button'
import { addLog } from '@/lib/storage'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })

    // Log the error
    addLog({
      level: 'error',
      message: 'Application error caught by error boundary',
      details: {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      }
    })

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', error, errorInfo)
    }
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  handleReportError = () => {
    const errorReport = {
      error: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      url: window.location.href
    }

    // Copy error report to clipboard
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))
      .then(() => {
        alert('Error report copied to clipboard. Please share this with the development team.')
      })
      .catch(() => {
        alert('Failed to copy error report. Please manually copy the error details from the console.')
      })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <div className="max-w-2xl w-full">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 text-center">
              <div className="mb-6">
                <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Something went wrong
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  We're sorry, but an unexpected error occurred. This has been logged and 
                  we'll work to fix it as soon as possible.
                </p>
              </div>

              {/* Error Details (Development Only) */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-left">
                  <h3 className="font-medium text-red-800 dark:text-red-200 mb-2">
                    Error Details (Development Mode)
                  </h3>
                  <div className="text-sm text-red-700 dark:text-red-300 space-y-2">
                    <div>
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    {this.state.error.stack && (
                      <div>
                        <strong>Stack Trace:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto bg-red-100 dark:bg-red-900/40 p-2 rounded">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <strong>Component Stack:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto bg-red-100 dark:bg-red-900/40 p-2 rounded">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  onClick={this.handleReload}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Reload Page
                </Button>
                
                <Button
                  variant="outline"
                  onClick={this.handleGoHome}
                  className="flex items-center gap-2"
                >
                  <Home className="w-4 h-4" />
                  Go to Home
                </Button>

                <Button
                  variant="outline"
                  onClick={this.handleReportError}
                  className="flex items-center gap-2"
                >
                  <Bug className="w-4 h-4" />
                  Report Error
                </Button>
              </div>

              {/* Additional Help */}
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  If this problem persists, try:
                </p>
                <ul className="text-sm text-gray-500 dark:text-gray-400 mt-2 space-y-1">
                  <li>• Clearing your browser cache and cookies</li>
                  <li>• Disabling browser extensions</li>
                  <li>• Using an incognito/private browsing window</li>
                  <li>• Checking your internet connection</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

// Higher-order component for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  return function WrappedComponent(props: P) {
    return (
      <ErrorBoundary fallback={fallback}>
        <Component {...props} />
      </ErrorBoundary>
    )
  }
}

// Hook for error handling in functional components
export function useErrorHandler() {
  const handleError = (error: Error, errorInfo?: any) => {
    addLog({
      level: 'error',
      message: 'Error handled by useErrorHandler',
      details: {
        error: error.message,
        stack: error.stack,
        errorInfo
      }
    })

    if (process.env.NODE_ENV === 'development') {
      console.error('Error handled:', error, errorInfo)
    }
  }

  return handleError
}

// Async error handler for promises
export function handleAsyncError(error: any, context?: string) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error'
  
  addLog({
    level: 'error',
    message: `Async error${context ? ` in ${context}` : ''}`,
    details: {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      context
    }
  })

  if (process.env.NODE_ENV === 'development') {
    console.error(`Async error${context ? ` in ${context}` : ''}:`, error)
  }
}

// Global error handler setup
export function setupGlobalErrorHandling() {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    handleAsyncError(event.reason, 'unhandled promise rejection')
    
    // Prevent the default browser behavior
    event.preventDefault()
  })

  // Handle global errors
  window.addEventListener('error', (event) => {
    addLog({
      level: 'error',
      message: 'Global error caught',
      details: {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.stack
      }
    })

    if (process.env.NODE_ENV === 'development') {
      console.error('Global error:', event)
    }
  })
}
