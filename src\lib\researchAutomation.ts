import { Project, ResearchPlan, Link, Interaction, ScrapingResult } from '@/types'
import { addLog, saveProject, getSettings } from './storage'
import { scrapeUrl, searchAndScrape } from './firecrawl'
import { summarizeContent, generateAIResponse } from './openai'

export class ResearchAutomation {
  private project: Project
  private onProjectUpdate: (project: Project) => void
  private isRunning: boolean = false

  constructor(project: Project, onProjectUpdate: (project: Project) => void) {
    this.project = project
    this.onProjectUpdate = onProjectUpdate
  }

  async startAutomatedResearch(): Promise<void> {
    if (this.isRunning) {
      addLog({
        level: 'warning',
        message: 'Research automation already running',
        details: { projectId: this.project.id }
      })
      return
    }

    if (!this.project.researchPlan?.approved) {
      addLog({
        level: 'error',
        message: 'Cannot start research without approved plan',
        details: { projectId: this.project.id }
      })
      return
    }

    this.isRunning = true
    
    try {
      await this.executeResearchPlan()
    } catch (error) {
      addLog({
        level: 'error',
        message: 'Research automation failed',
        details: { 
          projectId: this.project.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })
    } finally {
      this.isRunning = false
    }
  }

  private async executeResearchPlan(): Promise<void> {
    const settings = getSettings()
    if (!settings.firecrawlApiKey) {
      addLog({
        level: 'warning',
        message: 'Firecrawl API key not configured, skipping web scraping'
      })
      return
    }

    const plan = this.project.researchPlan!
    
    // Add system message about starting research
    this.addInteraction({
      type: 'system',
      content: `Starting automated research with ${plan.queries.length} search queries...`
    })

    let citationCounter = this.project.links.length + 1

    // Process each query
    for (let i = 0; i < plan.queries.length; i++) {
      const query = plan.queries[i]
      
      this.addInteraction({
        type: 'system',
        content: `Searching: "${query}" (${i + 1}/${plan.queries.length})`
      })

      try {
        // Search and scrape
        const results = await searchAndScrape(query, settings.firecrawlApiKey, 3)
        
        if (results.length === 0) {
          this.addInteraction({
            type: 'system',
            content: `No results found for query: "${query}"`
          })
          continue
        }

        // Process each result
        for (const result of results) {
          if (result.success && result.content) {
            // Summarize content
            let summary = ''
            if (settings.openaiApiKey) {
              try {
                summary = await summarizeContent(
                  result.content,
                  `MUN research for ${this.project.delegate} in ${this.project.committee} on ${this.project.agenda}`,
                  settings.openaiApiKey,
                  settings.selectedModel
                )
              } catch (summaryError) {
                addLog({
                  level: 'warning',
                  message: 'Failed to summarize content',
                  details: { url: result.url }
                })
                summary = result.content.substring(0, 500) + '...'
              }
            } else {
              summary = result.content.substring(0, 500) + '...'
            }

            // Add link
            const link: Link = {
              id: crypto.randomUUID(),
              url: result.url,
              title: result.title,
              description: summary,
              citationNumber: citationCounter++,
              scrapedContent: result.content,
              addedAt: new Date().toISOString()
            }

            this.project.links.push(link)

            // Update context with new information
            const contextAddition = `\n\n## Source [${link.citationNumber}]: ${link.title}\n${summary}\n`
            this.project.context += contextAddition

            this.addInteraction({
              type: 'system',
              content: `Added source [${link.citationNumber}]: ${link.title}`
            })
          }
        }

        // Add delay between queries to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000))
        
      } catch (error) {
        this.addInteraction({
          type: 'system',
          content: `Error processing query "${query}": ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    }

    // Generate research summary
    if (settings.openaiApiKey && this.project.links.length > 0) {
      try {
        const summaryPrompt = `Based on the research conducted, provide a comprehensive summary for the MUN delegate representing ${this.project.delegate} in ${this.project.committee} on the topic: ${this.project.agenda}

Key areas to cover:
1. Country position and policy stance
2. Historical context and precedents
3. Current developments
4. Potential allies and opposition
5. Key recommendations for the delegate

Research sources gathered: ${this.project.links.length} sources`

        const response = await generateAIResponse(
          [{ role: 'user', content: summaryPrompt }],
          settings.openaiApiKey,
          settings.selectedModel,
          `You are providing a research summary for MUN preparation. Focus on actionable insights for the delegate.`
        )

        this.addInteraction({
          type: 'ai',
          content: `## Research Summary\n\n${response.content}`,
          metadata: { type: 'research_summary' }
        })

      } catch (error) {
        addLog({
          level: 'warning',
          message: 'Failed to generate research summary',
          details: { error: error instanceof Error ? error.message : 'Unknown error' }
        })
      }
    }

    // Mark research as completed
    this.project.status = 'completed'
    this.addInteraction({
      type: 'system',
      content: `Research completed! Gathered ${this.project.links.length} sources and updated research context.`
    })

    addLog({
      level: 'success',
      message: 'Automated research completed',
      details: {
        projectId: this.project.id,
        sourcesGathered: this.project.links.length,
        queriesProcessed: plan.queries.length
      }
    })
  }

  private addInteraction(interaction: Omit<Interaction, 'id' | 'timestamp'>): void {
    const newInteraction: Interaction = {
      ...interaction,
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString()
    }

    this.project.interactions.push(newInteraction)
    this.saveAndUpdate()
  }

  private saveAndUpdate(): void {
    saveProject(this.project)
    this.onProjectUpdate(this.project)
  }

  stop(): void {
    this.isRunning = false
    addLog({
      level: 'info',
      message: 'Research automation stopped',
      details: { projectId: this.project.id }
    })
  }

  isActive(): boolean {
    return this.isRunning
  }
}

// Utility function to create and start research automation
export async function startAutomatedResearch(
  project: Project,
  onProjectUpdate: (project: Project) => void
): Promise<ResearchAutomation> {
  const automation = new ResearchAutomation(project, onProjectUpdate)
  await automation.startAutomatedResearch()
  return automation
}
