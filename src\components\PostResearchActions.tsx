import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Dialog } from '@/components/ui/Dialog'
import { Project } from '@/types'
import { getSettings, addLog } from '@/lib/storage'
import { generateAIResponse } from '@/lib/openai'
import { 
  FileText, 
  MessageSquare, 
  Download, 
  Wand2, 
  X,
  Copy,
  CheckCircle
} from 'lucide-react'

interface PostResearchActionsProps {
  project: Project
  onProjectUpdate: (project: Project) => void
}

export function PostResearchActions({ project, onProjectUpdate }: PostResearchActionsProps) {
  const [showAssistDialog, setShowAssistDialog] = useState(false)
  const [assistRequest, setAssistRequest] = useState('')
  const [assistType, setAssistType] = useState<'speech' | 'poi' | 'summary' | 'custom'>('speech')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const [copied, setCopied] = useState(false)

  const isResearchComplete = project.status === 'completed' && project.links.length > 0

  const formatResearch = () => {
    const formattedContent = `# MUN Research Report
## ${project.committee} - ${project.agenda}
**Delegate:** ${project.delegate}
**Generated:** ${new Date().toLocaleDateString()}

---

## Executive Summary
${project.context}

---

## Research Sources (${project.links.length} sources)

${project.links.map(link => `
### [${link.citationNumber}] ${link.title}
**URL:** ${link.url}
**Added:** ${new Date(link.addedAt).toLocaleDateString()}

${link.description}

---
`).join('')}

## Research Interactions
${project.interactions
  .filter(i => i.type === 'ai' && i.metadata?.type === 'research_summary')
  .map(i => i.content)
  .join('\n\n---\n\n')}

---

*This report was generated by MUN Research Assistant*
`

    // Create and download file
    const blob = new Blob([formattedContent], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${project.committee}_${project.delegate}_Research.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    addLog({
      level: 'success',
      message: 'Research report exported',
      details: { projectId: project.id, sourcesIncluded: project.links.length }
    })
  }

  const generateAssistContent = async () => {
    const settings = getSettings()
    if (!settings.openaiApiKey) {
      addLog({
        level: 'error',
        message: 'OpenAI API key not configured'
      })
      return
    }

    setIsGenerating(true)
    
    try {
      let prompt = ''
      
      switch (assistType) {
        case 'speech':
          prompt = `Based on the research conducted, write an opening speech for the MUN delegate representing ${project.delegate} in ${project.committee} on the topic: ${project.agenda}.

The speech should:
- Be 2-3 minutes long (approximately 300-400 words)
- Clearly state the country's position
- Reference key points from the research
- Be diplomatic yet assertive
- Include specific policy recommendations

Research context: ${project.context}

Additional request: ${assistRequest || 'Standard opening speech'}`
          break

        case 'poi':
          prompt = `Generate 5-7 strategic Points of Information (POIs) that the delegate representing ${project.delegate} could ask during the ${project.committee} debate on ${project.agenda}.

Each POI should:
- Be concise and direct
- Challenge opposing viewpoints or seek clarification
- Demonstrate knowledge of the topic
- Advance the delegate's position
- Be diplomatically phrased

Research context: ${project.context}

Specific focus: ${assistRequest || 'General strategic POIs'}`
          break

        case 'summary':
          prompt = `Create a comprehensive briefing summary for the MUN delegate representing ${project.delegate} in ${project.committee} on ${project.agenda}.

Include:
- Key country positions and policies
- Historical context and precedents
- Current developments and recent events
- Potential allies and opposition countries
- Strategic recommendations for committee sessions
- Important facts and statistics

Research context: ${project.context}

Focus areas: ${assistRequest || 'Complete briefing summary'}`
          break

        case 'custom':
          prompt = `${assistRequest}

Context: MUN delegate representing ${project.delegate} in ${project.committee} on the topic: ${project.agenda}

Research context: ${project.context}`
          break
      }

      const response = await generateAIResponse(
        [{ role: 'user', content: prompt }],
        settings.openaiApiKey,
        settings.selectedModel,
        'You are an expert MUN advisor helping delegates prepare for committee sessions. Provide practical, actionable content.'
      )

      setGeneratedContent(response.content)

      addLog({
        level: 'success',
        message: `Generated ${assistType} content`,
        details: { projectId: project.id, type: assistType }
      })

    } catch (error) {
      addLog({
        level: 'error',
        message: 'Failed to generate assist content',
        details: { 
          error: error instanceof Error ? error.message : 'Unknown error',
          type: assistType
        }
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedContent)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      addLog({
        level: 'error',
        message: 'Failed to copy to clipboard'
      })
    }
  }

  if (!isResearchComplete) {
    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
          Complete your research to access formatting and assistance features
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Research Complete Notification */}
      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <h3 className="font-medium text-green-800 dark:text-green-200">
            Research Completed!
          </h3>
        </div>
        <p className="text-sm text-green-700 dark:text-green-300">
          Your research is complete with {project.links.length} sources gathered. 
          You can now format your research or get AI assistance for speeches and POIs.
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        <Button
          onClick={formatResearch}
          className="flex items-center gap-2"
        >
          <Download className="w-4 h-4" />
          Export Research Report
        </Button>

        <Button
          variant="outline"
          onClick={() => setShowAssistDialog(true)}
          className="flex items-center gap-2"
        >
          <Wand2 className="w-4 h-4" />
          AI Assistance
        </Button>
      </div>

      {/* Assist Dialog */}
      {showAssistDialog && (
        <Dialog open={showAssistDialog} onOpenChange={setShowAssistDialog}>
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b">
                <h2 className="text-2xl font-semibold">AI Assistance</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAssistDialog(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="p-6 space-y-6">
                {/* Content Type Selection */}
                <div>
                  <label className="block text-sm font-medium mb-3">
                    What would you like to generate?
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {[
                      { value: 'speech', label: 'Opening Speech', icon: MessageSquare },
                      { value: 'poi', label: 'Points of Information', icon: MessageSquare },
                      { value: 'summary', label: 'Briefing Summary', icon: FileText },
                      { value: 'custom', label: 'Custom Request', icon: Wand2 }
                    ].map(({ value, label, icon: Icon }) => (
                      <Button
                        key={value}
                        variant={assistType === value ? "default" : "outline"}
                        onClick={() => setAssistType(value as any)}
                        className="flex flex-col items-center gap-2 h-auto py-3"
                      >
                        <Icon className="w-4 h-4" />
                        <span className="text-xs">{label}</span>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Request Input */}
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {assistType === 'custom' ? 'Your Request' : 'Additional Instructions (Optional)'}
                  </label>
                  <textarea
                    className="w-full h-24 px-3 py-2 border border-input rounded-md bg-background text-sm resize-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                    placeholder={
                      assistType === 'custom' 
                        ? 'Describe what you need help with...'
                        : 'Any specific requirements or focus areas...'
                    }
                    value={assistRequest}
                    onChange={(e) => setAssistRequest(e.target.value)}
                  />
                </div>

                {/* Generate Button */}
                <Button
                  onClick={generateAssistContent}
                  disabled={isGenerating || (assistType === 'custom' && !assistRequest.trim())}
                  className="w-full flex items-center gap-2"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4" />
                      Generate Content
                    </>
                  )}
                </Button>

                {/* Generated Content */}
                {generatedContent && (
                  <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium">Generated Content</h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={copyToClipboard}
                        className="flex items-center gap-2"
                      >
                        {copied ? (
                          <>
                            <CheckCircle className="w-4 h-4" />
                            Copied!
                          </>
                        ) : (
                          <>
                            <Copy className="w-4 h-4" />
                            Copy
                          </>
                        )}
                      </Button>
                    </div>
                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      <pre className="whitespace-pre-wrap text-sm">{generatedContent}</pre>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Dialog>
      )}
    </div>
  )
}
